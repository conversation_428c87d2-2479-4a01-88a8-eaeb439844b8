# CORS跨域问题解决方案

## 问题描述

在浏览器中，由于同源策略（Same-Origin Policy），网页无法直接访问不同域名的API。当插件尝试从 `login.augmentcode.com` 访问 `tempmail.plus` 的API时，会遇到CORS错误：

```
Access to fetch at 'https://tempmail.plus/api/mails?email=...' from origin 'https://login.augmentcode.com' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 解决方案

我们通过Chrome扩展的Background Script来解决这个问题。Background Script运行在扩展的上下文中，不受网页的同源策略限制。

### 架构设计

```
Content Script (网页中) 
    ↓ 发送消息
Background Script (扩展中)
    ↓ 发起API请求
Tempmail.plus API
    ↓ 返回数据
Background Script
    ↓ 发送响应
Content Script
```

### 实现细节

#### 1. Background Script (background.js)
- 监听来自Content Script的消息
- 代理API请求到tempmail.plus
- 返回API响应数据

#### 2. Content Script (content.js)
- 通过 `chrome.runtime.sendMessage` 发送请求
- 等待Background Script的响应
- 处理返回的数据

#### 3. Manifest配置
- 添加 `host_permissions` 允许访问tempmail.plus
- 配置Background Script

## 代码实现

### Background Script消息处理
```javascript
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'fetchTempMail') {
    handleTempMailRequest(message, sendResponse);
    return true; // 保持消息通道开放
  }
});
```

### Content Script API调用
```javascript
function sendMessageToBackground(message) {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, (response) => {
      if (response.success) {
        resolve(response.data);
      } else {
        reject(new Error(response.error));
      }
    });
  });
}
```

## 优势

1. **绕过CORS限制**: Background Script不受同源策略限制
2. **安全性**: 扩展权限控制，只能访问指定域名
3. **透明性**: Content Script无需关心跨域问题
4. **可靠性**: 统一的错误处理和重试机制

## 调试方法

### 查看Background Script日志
1. 打开 `chrome://extensions/`
2. 找到插件，点击"检查视图 service worker"
3. 在Console中查看API请求日志

### 查看Content Script日志
1. 在目标网页按F12
2. 在Console中查看消息发送日志

## 注意事项

1. **权限配置**: 确保manifest.json中配置了正确的host_permissions
2. **消息格式**: 保持Content Script和Background Script之间的消息格式一致
3. **错误处理**: 处理网络错误、API错误等各种异常情况
4. **性能考虑**: 避免频繁的消息传递，合理设置重试间隔

## 测试验证

更新插件后，应该能看到：
- Content Script中不再有CORS错误
- Background Script中有API请求成功的日志
- 验证码能够正常获取和填入

这个解决方案完全符合Chrome扩展的最佳实践，既解决了跨域问题，又保持了良好的安全性和可维护性。
