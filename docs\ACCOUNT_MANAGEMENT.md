# 邮箱账户管理功能

## 功能概述

插件现在支持自动保存通过"续杯"按钮创建的邮箱账户，方便用户后续查看、复制和管理这些账户。

## 主要功能

### 1. 自动保存账户
- **触发条件**: 仅当用户点击"续杯"按钮创建邮箱时才会保存
- **保存内容**: 邮箱地址、创建时间、时间戳
- **存储位置**: Chrome扩展的本地同步存储
- **数量限制**: 最多保存50个账户，超出时自动删除最旧的

### 2. 账户列表显示
- **位置**: 插件设置页面下方
- **排序**: 最新创建的账户显示在最前面（按时间戳倒序）
- **信息**: 显示邮箱地址和详细的创建时间
- **滚动**: 支持滚动查看，最大高度200px
- **时间格式**: 显示本地化的创建时间（如：2025-06-10 14:30:25）

### 3. 账户操作

#### 复制功能
- **操作**: 点击"复制"按钮
- **功能**: 将邮箱地址复制到系统剪贴板
- **反馈**: 显示复制成功或失败的提示信息

#### 删除功能
- **操作**: 点击"删除"按钮
- **功能**: 从列表中移除指定的邮箱账户
- **确认**: 无需确认，直接删除
- **反馈**: 显示删除成功的提示信息

#### 清空功能
- **操作**: 点击"清空所有账户"按钮
- **功能**: 删除所有保存的邮箱账户
- **确认**: 需要用户确认操作
- **反馈**: 显示清空成功的提示信息

## 界面设计

### 账户项样式
```
┌─────────────────────────────────────────────┐
│ <EMAIL>          [复制] [删除]  │
│ 创建时间: 2025-06-10 14:30:25              │
└─────────────────────────────────────────────┘
```

### 快速输入功能
- **位置**: 登录页面邮箱输入框右侧
- **图标**: 📧 按钮
- **功能**: 点击显示已创建账户的下拉菜单
- **选择**: 点击任意邮箱即可快速填入

### 空状态显示
```
┌─────────────────────────────────────────────┐
│            暂无已创建的邮箱账户             │
└─────────────────────────────────────────────┘
```

## 数据结构

### 账户对象
```javascript
{
  email: "<EMAIL>",        // 邮箱地址
  createdAt: "2025-06-10 14:30:25",   // 创建时间（本地格式）
  timestamp: *************             // 时间戳（用于排序）
}
```

### 存储格式
```javascript
{
  createdAccounts: [
    {
      email: "<EMAIL>",
      createdAt: "2025-06-10 14:30:25",
      timestamp: *************
    },
    // ... 更多账户
  ]
}
```

## 技术实现

### 保存逻辑
1. 检测续杯按钮点击状态
2. 在生成随机邮箱时触发保存
3. 检查是否已存在相同邮箱
4. 添加到账户列表前端
5. 限制数量并保存到存储

### 界面更新
1. 从存储加载账户列表
2. 动态生成HTML元素
3. 绑定事件处理器
4. 实时更新显示状态

### 剪贴板操作
- 使用现代 `navigator.clipboard.writeText()` API
- 提供错误处理和用户反馈
- 兼容性良好的实现方式

## 使用场景

### 场景一：批量注册
1. 多次点击续杯按钮创建不同账户
2. 在设置页面查看所有创建的账户
3. 根据需要复制特定账户进行使用

### 场景二：账户管理
1. 定期清理不需要的账户
2. 复制常用账户到其他地方
3. 查看账户创建历史

### 场景三：备份恢复
1. 账户数据自动同步到Chrome账户
2. 在不同设备间共享账户列表
3. 重装插件后自动恢复账户

## 隐私和安全

### 数据保护
- 数据仅存储在用户本地
- 通过Chrome同步服务加密传输
- 不会上传到任何第三方服务器

### 用户控制
- 用户可以随时删除任何账户
- 支持一键清空所有数据
- 完全由用户控制数据的生命周期

### 存储限制
- 最多保存50个账户，防止存储空间滥用
- 自动清理最旧的账户
- 合理的存储空间使用

## 注意事项

1. **仅保存续杯创建的账户**: 不会保存手动输入或其他方式的邮箱
2. **本地存储**: 数据存储在Chrome扩展的本地存储中
3. **同步功能**: 如果启用Chrome同步，账户会在设备间同步
4. **隐私保护**: 建议定期清理不需要的账户
5. **备份建议**: 重要账户建议手动备份到其他地方

这个功能大大提升了插件的实用性，让用户可以更好地管理和使用创建的邮箱账户。
