# 隐私保护设计

## 设计理念

为了保护用户隐私和开发者信息，插件采用了分离式邮箱配置设计，确保个人邮箱信息不会暴露给其他用户。

## 隐私保护措施

### 1. 邮箱信息分离

**问题**: 如果直接要求用户输入完整的临时邮箱地址，可能会暴露开发者的个人邮箱信息。

**解决方案**: 
- 用户只需输入邮箱的用户名部分（如 `myname123`）
- 系统自动添加固定的域名后缀 `@mailto.plus`
- 这样既保护了隐私，又确保了功能的正常使用

### 2. 配置界面优化

**用户界面**:
```
临时邮箱前缀: [myname123] @mailto.plus
```

**用户体验**:
- 清晰显示用户需要输入的部分
- 自动显示固定的域名后缀
- 避免用户输入错误的域名

### 3. 数据存储安全

**存储内容**:
- 只存储用户输入的前缀部分
- 不存储完整的邮箱地址
- 在使用时动态组合完整地址

**存储位置**:
- 使用Chrome扩展的 `chrome.storage.sync`
- 数据加密存储在用户本地
- 不会上传到任何服务器

## 技术实现

### 配置保存
```javascript
// 只保存前缀部分
const dataToSave = {
  tempEmailPrefix: userInput  // 如: "myname123"
};
```

### 邮箱组合
```javascript
// 使用时动态组合
const fullEmail = prefix + '@mailto.plus';
```

### 输入验证
```javascript
// 确保前缀格式正确
const prefixRegex = /^[a-zA-Z0-9_-]+$/;
```

## 用户指南

### 如何获取临时邮箱前缀

1. **访问临时邮箱服务**:
   - 打开 https://tempmail.plus/
   - 系统会自动生成临时邮箱（如 `<EMAIL>`）

2. **提取前缀**:
   - 复制 @ 符号前面的部分（如 `abc123`）
   - 这就是你需要在插件中配置的前缀

3. **配置插件**:
   - 在插件设置中输入前缀 `abc123`
   - 系统会自动组合成 `<EMAIL>`

### 前缀命名建议

- **使用随机字符**: 如 `user123abc`、`temp456xyz`
- **避免个人信息**: 不要使用真实姓名、生日等
- **保持唯一性**: 确保前缀不会与他人冲突
- **定期更换**: 建议定期更换前缀以提高安全性

## 安全注意事项

### 1. 前缀保密
- 不要在公开场合分享你的临时邮箱前缀
- 避免使用容易被猜测的前缀

### 2. 定期更换
- 建议每隔一段时间更换新的前缀
- 如果发现邮箱被滥用，立即更换

### 3. 使用限制
- 临时邮箱仅用于接收验证码
- 不要用于重要账户的注册
- 注意临时邮箱的有效期限制

## 开发者说明

这种设计确保了：
- 开发者的个人邮箱信息不会暴露
- 用户可以使用自己的临时邮箱前缀
- 插件功能正常运行
- 符合隐私保护最佳实践

通过这种分离式设计，既保护了所有人的隐私，又保持了插件的易用性和功能完整性。
