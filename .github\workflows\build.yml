name: Build Extension

on:
  push:
    branches: [ master, develop ]
  pull_request:
    branches: [ master ]
  release:
    types: [ created ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build extension
      run: npm run build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: extension-build
        path: dist/

    - name: Upload release assets
      if: github.event_name == 'release'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: dist/augment-refill-extension_*.zip
        asset_name: augment-refill-extension-${{ github.event.release.tag_name }}.zip
        asset_content_type: application/zip
