# 配置示例

## 插件配置说明

在使用Augment续杯插件之前，您需要在插件设置中配置以下三个参数：

### 1. 邮箱后缀
- **用途**: 用于生成注册邮箱地址
- **格式**: 域名格式，如 `example.com`
- **示例**: `mydomain.com`, `test.org`, `company.net`
- **说明**: 可以是任意域名，不需要真实存在

### 2. 临时邮箱前缀
- **用途**: 用于接收Augment发送的验证码邮件
- **格式**: 邮箱用户名部分（只能包含字母、数字、下划线、连字符）
- **示例**: `myname123`
- **说明**: 系统会自动添加 @mailto.plus 后缀，组成完整的临时邮箱地址

### 3. 随机字符串位数
- **用途**: 控制生成邮箱地址中随机部分的长度
- **格式**: 1-32之间的数字
- **默认值**: 12
- **示例**: 8, 12, 16

## 完整配置示例

```
邮箱后缀: example.com
临时邮箱前缀: myname123
随机字符串位数: 12
```

## 工作流程说明

1. **注册邮箱生成**: 插件会生成类似 `<EMAIL>` 的随机邮箱用于注册
2. **验证码接收**: Augment系统发送的验证码邮件会被发送到 `<EMAIL>`
3. **自动获取**: 插件通过API从临时邮箱中获取验证码
4. **自动填入**: 验证码被自动填入验证页面并提交

## 注意事项

- 临时邮箱前缀只能包含字母、数字、下划线和连字符
- 邮箱后缀可以是任意域名，不需要真实存在
- 随机字符串位数建议保持默认的12位，既保证唯一性又不会过长

## 获取临时邮箱前缀

推荐使用 mailto.plus 服务：
1. 访问 https://tempmail.plus/
2. 系统会自动生成一个临时邮箱地址（如 `<EMAIL>`）
3. 复制邮箱地址中 @ 符号前面的部分（如 `abc123`）
4. 将这个前缀配置到插件设置中

## 常见问题

**Q: 为什么需要两个不同的邮箱配置？**
A: 注册邮箱用于创建Augment账户，临时邮箱用于接收验证码。这样设计可以确保验证码能够被正确接收和处理。

**Q: 临时邮箱会过期吗？**
A: 是的，临时邮箱通常有时效性。如果发现无法接收验证码，请更新临时邮箱前缀。

**Q: 可以使用其他临时邮箱服务吗？**
A: 目前插件专门适配了 mailto.plus 的API，建议使用该服务以确保最佳兼容性。
