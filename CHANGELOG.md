# 更新日志

本文档记录了项目的所有重要更改。

## [1.0.1] - 2025-06-10

### 🎉 重大更新

基于 [lisniuse/free-augment-code](https://github.com/lisniuse/free-augment-code) 进行大幅功能增强。

### ✨ 新增功能

#### 邮箱账户管理
- 自动保存通过续杯按钮创建的邮箱账户
- 显示详细的创建时间信息
- 支持复制、删除单个账户
- 支持一键清空所有账户
- 账户按时间倒序排列，最多保存50个

#### 快速输入功能
- 在登录页面邮箱输入框旁添加📧快速输入按钮
- 点击显示已创建账户的下拉菜单
- 一键选择邮箱快速填入
- 自动触发后续验证码获取流程

#### 智能触发机制
- 防止意外触发：只有明确操作才会自动执行
- 邮箱域名检测：使用配置域名的邮箱时自动获取验证码
- 状态管理：页面跳转时自动重置状态

#### 隐私保护设计
- 分离式邮箱配置：用户只需输入前缀，系统自动添加域名后缀
- 本地数据存储：所有数据存储在用户本地
- 加密同步：通过Chrome同步服务安全传输

### 🔧 技术改进

#### CORS问题解决
- 通过Background Script处理API请求
- 解决了直接从Content Script访问外部API的跨域问题
- 统一的错误处理和响应格式

#### 调试功能增强
- 详细的控制台日志输出
- 使用表情符号标识不同类型的操作
- 完整的错误堆栈跟踪
- 分离的Content Script和Background Script日志

#### 代码结构优化
- 模块化的函数设计
- 完善的错误处理机制
- 统一的状态管理
- 详细的代码注释

### 📚 文档完善

- 新增详细的使用说明文档
- 添加功能原理和技术实现说明
- 提供完整的调试指南
- 包含隐私保护和安全说明

### 🐛 问题修复

- 修复了邮件列表中发件人信息不准确的问题
- 解决了验证码页面意外触发的问题
- 优化了按钮添加的时机和逻辑
- 改进了表单事件触发机制

## [1.0.0] - 原始版本

### 基础功能
- 在Augment登录页面添加续杯按钮
- 自动生成随机邮箱并填入表单
- 基础的验证码获取功能

---

## 版本说明

- **主版本号**：重大功能更新或架构变更
- **次版本号**：新功能添加
- **修订版本号**：问题修复和小改进

## 贡献

欢迎提交Issue和Pull Request来帮助改进项目！
