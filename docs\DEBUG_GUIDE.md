# 调试指南

## 如何查看插件日志

### 1. 查看Content Script日志
- 在Augment登录页面按 `F12` 或右键选择"检查"
- 切换到 `Console` 标签页

### 2. 查看Background Script日志
- 打开Chrome扩展管理页面 (`chrome://extensions/`)
- 找到"Augment续杯"扩展
- 点击"检查视图 service worker"
- 在新打开的开发者工具中查看Console标签

### 3. 查看日志输出
插件会输出详细的日志信息，包含以下图标标识：

- 🚀 流程开始
- ✅ 成功操作
- ❌ 错误信息
- 🔍 查找/检查操作
- 📧 邮箱相关
- 🔄 重试/循环操作
- ⏳ 等待操作
- 📡 API调用
- 🎯 验证码相关

### 4. 常见日志模式

#### 正常流程日志：

**从登录页面开始：**
```
🔍 检查页面URL: https://login.augmentcode.com/u/login/identifier
✅ 检测到Augment登录页面
🚀 Augment续杯: 开始续杯流程
✅ 配置获取成功:
📧 临时邮箱 (接收验证码): <EMAIL>
📧 随机邮箱 (用于注册): <EMAIL>
✅ 找到邮箱输入框
🚀 已点击继续按钮
```

**验证码页面自动处理（续杯按钮触发）：**
```
🔍 检查页面URL: https://login.augmentcode.com/u/login/passwordless-email-challenge
✅ 检测到Augment验证码页面
🔍 检查是否需要自动获取验证码...
🚀 续杯按钮触发 - 开始自动获取验证码流程
📧 页面显示的注册邮箱: <EMAIL>
📧 配置的临时邮箱: <EMAIL>
🚀 Augment续杯: 开始获取验证码流程
📡 开始调用临时邮箱API...
🌐 Background: 处理临时邮箱API请求
📡 Background: API响应状态 200 OK
✅ 成功获取到验证码: 123456
📝 开始填入验证码: 123456
✅ 已点击确认按钮
```

**验证码页面自动处理（邮箱域名匹配）：**
```
🔍 检查页面URL: https://login.augmentcode.com/u/login/passwordless-email-challenge
✅ 检测到Augment验证码页面
🔍 检查是否需要自动获取验证码...
🔍 检查是否使用了配置的邮箱域名...
📧 页面显示的注册邮箱: <EMAIL>
🔧 配置的邮箱域名: example.com
✅ 检测到使用了配置的邮箱域名，开始自动获取验证码
🎯 开始自动验证码获取流程
```

## 常见问题排查

### 问题1: 续杯按钮没有出现
**查看日志:**
- 查找 `🔍 检查页面URL` 确认是否在正确页面
- 查找 `🔘 开始添加续杯按钮` 确认是否尝试添加按钮
- 查找 `✅ 找到原始按钮` 确认是否找到原始按钮

**可能原因:**
- 页面URL不匹配
- 原始按钮选择器变化
- 页面加载时机问题

### 问题2: 点击续杯按钮没有反应
**查看日志:**
- 查找 `🚀 Augment续杯: 开始续杯流程` 确认点击事件触发
- 查找 `❌ 未设置邮箱后缀` 或 `❌ 未设置临时邮箱地址` 确认配置问题

**可能原因:**
- 插件配置未设置
- 配置读取失败

### 问题3: 无法获取验证码
**查看日志:**
- 查找 `📡 开始调用临时邮箱API` 确认API调用开始
- 查找 `🌐 Background: 处理临时邮箱API请求` 确认background处理
- 查找 `📡 Background: API响应状态` 确认API响应
- 查找 `📬 找到X封邮件` 确认邮件数量
- 查找 `✅ 找到来自Augment的邮件` 确认找到目标邮件

**可能原因:**
- 临时邮箱地址配置错误
- 网络连接问题
- API服务不可用
- 邮件尚未到达
- CORS跨域问题（已通过background script解决）

### 问题4: 邮件过滤错误
**查看日志:**
- 查找 `📧 检查第X封邮件` 查看邮件列表信息
- 查找 `📧 邮件详情 - 发件人` 查看真实发件人
- 查找 `✅ 确认这是来自Augment的邮件` 确认识别结果

**重要发现:**
- 邮件列表中的发件人可能不准确（如显示为 `<EMAIL>`）
- 需要获取邮件详情才能看到真实发件人（如 `<EMAIL>`）
- 插件现在会检查所有邮件详情，不在列表阶段过滤

### 问题5: 验证码提取失败
**查看日志:**
- 查找 `🔍 开始提取验证码` 确认提取开始
- 查找 `📄 邮件内容预览` 查看邮件内容
- 查找 `🔍 尝试模式X` 查看匹配尝试
- 查找 `✅ 验证码格式正确` 确认提取结果

**可能原因:**
- 邮件格式变化
- 验证码格式不匹配
- 邮件内容解析问题

### 问题6: 不应该自动触发但触发了
**查看日志:**
- 查找 `🔄 已重置插件状态` 确认状态重置
- 查找 `✅ 设置续杯按钮点击状态` 确认按钮状态
- 查找 `🔍 检查是否需要自动获取验证码` 确认触发条件

**可能原因:**
- 状态管理异常
- 页面跳转检测失败
- 邮箱域名意外匹配

### 问题7: 应该自动触发但没有触发
**查看日志:**
- 查找 `🔧 配置的邮箱域名` 确认域名配置
- 查找 `📧 页面显示的注册邮箱` 确认页面邮箱
- 查找 `❌ 注册邮箱域名不匹配` 确认匹配结果

**可能原因:**
- 邮箱域名配置错误
- 页面邮箱提取失败
- 域名匹配逻辑问题

### 问题8: 验证码填入失败
**查看日志:**
- 查找 `📝 开始填入验证码` 确认填入开始
- 查找 `✅ 找到验证码输入框` 确认找到输入框
- 查找 `✅ 找到确认按钮` 确认找到提交按钮

**可能原因:**
- 页面结构变化
- 输入框选择器变化
- 按钮选择器变化

## 手动调试步骤

### 1. 检查配置
在Console中执行：
```javascript
chrome.storage.sync.get(['emailDomain', 'tempEmailPrefix', 'randomLength'], console.log);
```

### 2. 手动触发续杯
在Console中执行：
```javascript
handleRefill();
```

### 3. 手动获取验证码
在Console中执行：
```javascript
getVerificationCodeFromTempMail('<EMAIL>').then(console.log);
```

### 4. 检查页面元素
在Console中执行：
```javascript
// 检查邮箱输入框
console.log('邮箱输入框:', document.querySelector('input[name="username"]'));

// 检查验证码输入框
console.log('验证码输入框:', document.querySelector('input[name="code"]'));

// 检查所有按钮
document.querySelectorAll('button').forEach((btn, i) => {
  console.log(`按钮${i+1}:`, btn.textContent.trim(), btn);
});
```

## 联系支持

如果按照以上步骤仍无法解决问题，请：

1. 复制完整的Console日志
2. 提供当前页面URL
3. 描述具体的问题现象
4. 提供插件配置截图

这些信息将帮助快速定位和解决问题。
