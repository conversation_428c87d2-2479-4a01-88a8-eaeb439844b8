// 后台脚本，用于管理扩展的生命周期
chrome.runtime.onInstalled.addListener(() => {
  console.log('Augment续杯扩展已安装');
});

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'log') {
    console.log('来自内容脚本的消息:', message.data);
  } else if (message.action === 'fetchTempMail') {
    // 处理临时邮箱API请求
    handleTempMailRequest(message, sendResponse);
    return true; // 保持消息通道开放以进行异步响应
  }
  return true;
});

// 处理临时邮箱API请求
async function handleTempMailRequest(message, sendResponse) {
  console.log('🌐 Background: 处理临时邮箱API请求', message);

  try {
    const { url, method = 'GET' } = message;

    console.log('📡 Background: 发起请求到', url);

    const response = await fetch(url, {
      method: method,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    console.log('📡 Background: API响应状态', response.status, response.statusText);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📋 Background: API响应数据', data);

    sendResponse({
      success: true,
      data: data,
      status: response.status
    });

  } catch (error) {
    console.error('❌ Background: API请求失败', error);
    sendResponse({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
}
