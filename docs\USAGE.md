# Augment续杯插件使用指南

## 快速开始

### 1. 安装插件
1. 下载最新的ZIP文件
2. 解压到任意文件夹
3. 打开Chrome浏览器，访问 `chrome://extensions/`
4. 开启右上角的"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择解压后的文件夹

### 2. 配置插件
点击浏览器工具栏中的插件图标，设置以下参数：

- **邮箱后缀**: 任意域名，如 `example.com`
- **临时邮箱前缀**: 邮箱用户名部分，如 `myname123`（系统会自动添加 @mailto.plus）
- **随机位数**: 建议保持默认的12位

点击"保存设置"。

### 3. 邮箱账户管理
插件会自动保存通过"续杯"按钮创建的邮箱账户：

- **查看账户**: 在设置页面下方可以看到所有已创建的邮箱账户
- **时间显示**: 每个账户都显示详细的创建时间
- **时间排序**: 账户按创建时间倒序排列，最新的在最前面
- **复制邮箱**: 点击"复制"按钮可以将邮箱地址复制到剪贴板
- **删除账户**: 点击"删除"按钮可以移除单个邮箱账户
- **清空所有**: 点击"清空所有账户"按钮可以删除所有保存的账户
- **自动限制**: 最多保存50个账户，超出时自动删除最旧的账户

### 4. 快速输入功能
在登录页面的邮箱输入框旁会出现一个📧按钮：

- **快速选择**: 点击📧按钮显示所有已创建的邮箱账户列表
- **一键填入**: 点击任意邮箱即可快速填入到输入框
- **智能触发**: 填入后会自动触发邮箱域名检测和验证码获取
- **便捷操作**: 无需手动输入，提高登录效率

### 5. 使用续杯功能

#### 方式一：从登录页面开始（推荐）
1. 访问 [Augment登录页面](https://login.augmentcode.com/)
2. 在页面上找到"续杯"按钮（位于原始Continue按钮下方）
3. 点击"续杯"按钮
4. 插件将自动完成以下流程：
   - 生成随机邮箱地址
   - 填入表单并提交
   - 跳转到验证码页面
   - 自动获取验证码
   - 自动填入验证码
   - 完成注册

#### 方式二：智能邮箱域名检测
1. 手动在登录页面输入使用配置邮箱域名的邮箱地址
2. 点击Continue跳转到验证码页面
3. 插件会自动检测邮箱域名，如果匹配配置的域名，将自动：
   - 从临时邮箱获取验证码
   - 填入验证码输入框
   - 点击Continue按钮

#### 方式三：手动验证码页面使用
1. 使用任意邮箱在登录页面输入并点击Continue
2. 页面跳转到验证码页面后，插件不会自动触发
3. 需要手动操作或使用方式一的续杯功能

## 注意事项

- 确保网络连接稳定，以便正常访问临时邮箱API
- 如果验证码获取失败，可能需要手动输入
- 整个流程大约需要30-60秒完成
- 请合理使用，遵守相关服务条款

## 故障排除

### 问题：点击续杯按钮没有反应
- 检查是否已正确配置邮箱后缀和临时邮箱前缀
- 确认当前页面是Augment登录页面
- 尝试刷新页面重试

### 问题：验证码获取失败
- 检查网络连接
- 确认临时邮箱前缀设置正确，如 `myname123`
- 等待更长时间，有时邮件发送会有延迟

### 问题：验证码填入后无法提交
- 检查验证码是否正确填入
- 手动点击确认按钮
- 刷新页面重新开始流程

## API说明

插件使用以下临时邮箱API：

- 获取邮件列表：`https://tempmail.plus/api/mails?email={临时邮箱地址}`
- 获取邮件详情：`https://tempmail.plus/api/mails/{邮件ID}?email={临时邮箱地址}`

这些API是公开的，无需认证即可使用。
