{"name": "augment-refill-extension", "version": "1.0.1", "description": "Augment续杯浏览器插件 - 支持自动验证码获取和邮箱账户管理", "private": true, "scripts": {"build": "node scripts/build.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["chrome-extension", "augment", "automation", "email-management", "verification-code"], "author": "flintttan", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/flintttan/augment-refill-extension.git"}, "bugs": {"url": "https://github.com/flintttan/augment-refill-extension/issues"}, "homepage": "https://github.com/flintttan/augment-refill-extension#readme", "devDependencies": {"archiver": "^5.3.1", "fs-extra": "^11.1.1", "rimraf": "^5.0.1"}}