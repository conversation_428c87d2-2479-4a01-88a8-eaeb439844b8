# 功能说明

## 智能触发机制

插件现在支持两种自动验证码获取方式，确保在正确的时机触发功能，避免不必要的自动操作。

## 触发方式

### 方式一：续杯按钮触发（主动模式）

**使用场景**: 用户主动选择使用插件功能

**触发条件**:
1. 用户在登录页面点击"续杯"按钮
2. 插件设置状态标志 `refillButtonClicked = true`
3. 页面跳转到验证码页面时自动开始获取验证码

**工作流程**:
```
用户点击续杯按钮 → 生成随机邮箱 → 自动提交 → 跳转验证码页面 → 自动获取验证码
```

**优势**:
- 完全自动化流程
- 用户明确意图
- 不会意外触发

### 方式二：邮箱域名检测（智能模式）

**使用场景**: 用户使用配置的邮箱域名登录现有账户

**触发条件**:
1. 用户手动输入邮箱地址
2. 邮箱地址的域名与插件配置的邮箱域名匹配
3. 页面跳转到验证码页面时自动开始获取验证码

**工作流程**:
```
用户输入邮箱 → 检测域名匹配 → 跳转验证码页面 → 自动获取验证码
```

**优势**:
- 支持现有账户登录
- 智能识别用户意图
- 减少手动操作

## 状态管理

### 全局状态对象
```javascript
window.augmentRefillState = {
  refillButtonClicked: false,     // 是否点击了续杯按钮
  autoVerificationStarted: false, // 是否已开始自动验证
  shouldAutoGetCode: false        // 是否应该自动获取验证码
};
```

### 状态重置机制
- **页面跳转时**: 自动重置所有状态
- **URL变化时**: 检测并重置状态
- **新会话开始**: 确保干净的初始状态

## 安全机制

### 防止意外触发
1. **明确的触发条件**: 只在特定条件下触发
2. **状态隔离**: 不同页面间状态独立
3. **用户控制**: 用户可以选择是否使用自动功能

### 域名匹配验证
```javascript
// 严格的域名匹配
if (registeredEmail.includes('@' + configuredDomain)) {
  // 触发自动获取验证码
}
```

## 配置要求

### 必需配置
- **邮箱后缀**: 用于生成注册邮箱和域名匹配
- **临时邮箱前缀**: 用于接收验证码

### 配置示例
```
邮箱后缀: example.com
临时邮箱前缀: myname123
随机字符串位数: 12
```

## 使用场景

### 场景一：新用户注册
1. 点击"续杯"按钮
2. 自动生成随机邮箱注册
3. 自动获取验证码完成注册

### 场景二：现有用户登录
1. 输入已有的 `<EMAIL>` 邮箱
2. 插件检测到域名匹配
3. 自动获取验证码完成登录

### 场景三：其他邮箱登录
1. 输入其他域名的邮箱（如 `<EMAIL>`）
2. 插件不会自动触发
3. 用户需要手动输入验证码

## 调试信息

### 关键日志标识
- `✅ 设置续杯按钮点击状态`: 确认按钮触发
- `🔍 检查是否使用了配置的邮箱域名`: 确认域名检测
- `✅ 检测到使用了配置的邮箱域名`: 确认匹配成功
- `❌ 注册邮箱域名不匹配`: 确认不匹配
- `🔄 已重置插件状态`: 确认状态重置

### 状态检查
在控制台执行以查看当前状态：
```javascript
console.log(window.augmentRefillState);
```

## 注意事项

1. **域名配置准确性**: 确保邮箱后缀配置正确
2. **临时邮箱有效性**: 确保临时邮箱前缀可用
3. **网络连接稳定**: 确保能正常访问临时邮箱API
4. **合理使用**: 遵守相关服务条款和使用规范

这种设计既保证了功能的智能化，又避免了不必要的自动触发，为用户提供了灵活且可控的使用体验。
