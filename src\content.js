// 全局状态管理
window.augmentRefillState = {
  refillButtonClicked: false,  // 是否点击了续杯按钮
  autoVerificationStarted: false,  // 是否已开始自动验证
  shouldAutoGetCode: false  // 是否应该自动获取验证码
};

// 检查当前URL是否匹配目标页面
function checkUrl() {
  const identifierUrl = 'login.augmentcode.com/u/login/identifier';
  const challengeUrl = 'login.augmentcode.com/u/login/passwordless-email-challenge';
  const currentUrl = window.location.href;

  console.log('🔍 检查页面URL:', currentUrl);
  console.log('🎯 登录页面URL模式:', identifierUrl);
  console.log('🎯 验证码页面URL模式:', challengeUrl);

  // 检查URL是否匹配登录页面
  if (currentUrl.includes(identifierUrl)) {
    console.log('✅ 检测到Augment登录页面');
    // 重置状态
    window.augmentRefillState.autoVerificationStarted = false;

    // 检查续杯按钮是否已存在
    const existingButton = document.querySelector('.refill-button-added');
    if (!existingButton) {
      console.log('🔘 续杯按钮不存在，准备添加');
      addRefillButton();
    } else {
      console.log('✅ 续杯按钮已存在');
    }

    // 检查快速输入按钮是否已存在
    const existingQuickInputButton = document.querySelector('.quick-input-button-added');
    if (!existingQuickInputButton) {
      console.log('🔘 快速输入按钮不存在，准备添加');
      addQuickInputButton();
    } else {
      console.log('✅ 快速输入按钮已存在');
    }
  }
  // 检查URL是否匹配验证码页面
  else if (currentUrl.includes(challengeUrl)) {
    console.log('✅ 检测到Augment验证码页面');

    // 检查是否应该自动获取验证码
    if (!window.augmentRefillState.autoVerificationStarted) {
      console.log('🔍 检查是否需要自动获取验证码...');

      // 情况1：用户点击了续杯按钮
      if (window.augmentRefillState.refillButtonClicked) {
        console.log('🚀 续杯按钮触发 - 开始自动获取验证码流程');
        window.augmentRefillState.autoVerificationStarted = true;
        startAutoVerificationProcess();
      }
      // 情况2：检查是否使用了配置的邮箱域名
      else {
        checkIfShouldAutoGetCode();
      }
    } else {
      console.log('✅ 验证码获取流程已启动');
    }
  } else {
    console.log('❌ 当前页面不是目标页面');
  }
}

// 添加续杯按钮
function addRefillButton() {
  console.log('🔘 开始添加续杯按钮...');

  let attempts = 0;
  const maxAttempts = 20; // 最多尝试10秒

  // 等待原始按钮加载
  const checkExist = setInterval(() => {
    attempts++;
    console.log(`🔍 第${attempts}次查找原始按钮...`);

    const originalButton = document.querySelector('button[name="action"][value="default"]');
    const existingRefillButton = document.querySelector('.refill-button-added');

    if (originalButton && !existingRefillButton) {
      clearInterval(checkExist);
      console.log('✅ 找到原始按钮，开始创建续杯按钮');
      console.log('🔘 原始按钮:', originalButton);

      // 创建续杯按钮
      const refillButton = document.createElement('button');
      refillButton.type = 'button';
      refillButton.textContent = '续杯';
      refillButton.className = 'refill-button-added'; // 添加特殊类名用于检测

      // 复制原始按钮的样式类
      console.log('🎨 复制原始按钮样式...');
      originalButton.classList.forEach(className => {
        refillButton.classList.add(className);
        console.log('  添加样式类:', className);
      });

      // 添加点击事件
      refillButton.addEventListener('click', handleRefill);
      console.log('🔗 已添加点击事件');

      // 将按钮插入到原始按钮后面
      originalButton.parentNode.insertBefore(refillButton, originalButton.nextSibling);
      console.log('📍 按钮已插入到DOM');

      // 设置标志，表示按钮已添加
      buttonAdded = true;
      // 停止观察DOM变化
      observer.disconnect();
      console.log('✅ 续杯按钮添加完成');
    } else if (existingRefillButton) {
      clearInterval(checkExist);
      console.log('✅ 续杯按钮已存在，跳过添加');
    } else if (attempts >= maxAttempts) {
      clearInterval(checkExist);
      console.log('⏰ 查找原始按钮超时，停止尝试');
      console.log('🔍 页面所有按钮:');
      document.querySelectorAll('button').forEach((btn, index) => {
        console.log(`  按钮${index + 1}:`, btn.textContent.trim(), btn.name, btn.value, btn);
      });
    }
  }, 500);
}

// 处理续杯按钮点击
function handleRefill() {
  console.log('🚀 Augment续杯: 开始续杯流程');
  console.log('🌐 当前页面URL:', window.location.href);

  // 设置续杯按钮点击状态
  window.augmentRefillState.refillButtonClicked = true;
  console.log('✅ 设置续杯按钮点击状态');

  // 获取配置的临时邮箱和生成随机邮箱
  Promise.all([getTempEmail(), generateRandomEmail()]).then(([tempEmail, randomEmail]) => {
    console.log('✅ 配置获取成功:');
    console.log('📧 临时邮箱 (接收验证码):', tempEmail);
    console.log('📧 随机邮箱 (用于注册):', randomEmail);

    // 填入随机邮箱到输入框
    const emailInput = document.querySelector('input[name="username"]');
    if (emailInput) {
      console.log('✅ 找到邮箱输入框:', emailInput);

      emailInput.value = randomEmail;
      console.log('📝 已设置邮箱值:', emailInput.value);

      // 触发input事件，确保表单验证能够识别值的变化
      const inputEvent = new Event('input', { bubbles: true });
      emailInput.dispatchEvent(inputEvent);
      console.log('🔄 已触发input事件');

      // 触发change事件
      const changeEvent = new Event('change', { bubbles: true });
      emailInput.dispatchEvent(changeEvent);
      console.log('🔄 已触发change事件');

      // 自动点击原始按钮，延迟1秒以确保表单验证有足够时间处理
      setTimeout(() => {
        const originalButton = document.querySelector('button[name="action"][value="default"]');
        if (originalButton) {
          console.log('✅ 找到继续按钮，准备点击:', originalButton);
          originalButton.click();
          console.log('🚀 已点击继续按钮');

          // 等待页面跳转到验证码页面，然后开始获取验证码
          setTimeout(() => {
            console.log('⏳ 3秒延迟后开始验证码获取流程');
            waitForVerificationCodeAndFill(tempEmail);
          }, 3000);
        } else {
          console.error('❌ 未找到继续按钮');
          console.log('🔍 页面所有按钮:');
          document.querySelectorAll('button').forEach((btn, index) => {
            console.log(`  按钮${index + 1}:`, btn.textContent.trim(), btn);
          });
        }
      }, 1000);
    } else {
      console.error('❌ 未找到邮箱输入框');
      console.log('🔍 页面所有输入框:');
      document.querySelectorAll('input').forEach((input, index) => {
        console.log(`  输入框${index + 1}:`, input.name, input.type, input);
      });
    }
  }).catch(error => {
    console.error('❌ 获取邮箱配置时出错:', error);
    console.error('错误详情:', error.message);
    alert('配置获取失败: ' + error.message);
  });
}

// 获取配置的临时邮箱地址
function getTempEmail() {
  console.log('🔧 获取临时邮箱配置...');

  return new Promise((resolve, reject) => {
    chrome.storage.sync.get(['tempEmailPrefix'], function(data) {
      console.log('📋 存储数据:', data);

      // 检查是否设置了临时邮箱前缀
      if (!data.tempEmailPrefix) {
        console.error('❌ 未设置临时邮箱前缀');
        alert('请先在扩展设置中设置临时邮箱前缀！');
        reject(new Error('未设置临时邮箱前缀'));
        return;
      }

      // 组合完整的临时邮箱地址
      const fullTempEmail = data.tempEmailPrefix + '@mailto.plus';
      console.log('✅ 临时邮箱地址:', fullTempEmail);
      resolve(fullTempEmail);
    });
  });
}

// 生成随机邮箱（用于注册）
function generateRandomEmail() {
  console.log('🎲 生成随机邮箱...');

  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;

  // 从存储中获取邮箱后缀和随机字符串位数，如果没有则使用默认值
  return new Promise((resolve, reject) => {
    chrome.storage.sync.get(['emailDomain', 'randomLength'], function(data) {
      console.log('📋 邮箱配置数据:', data);

      // 检查是否设置了邮箱后缀
      if (!data.emailDomain) {
        console.error('❌ 未设置邮箱后缀');
        alert('请先在扩展设置中设置邮箱后缀！');
        reject(new Error('未设置邮箱后缀'));
        return;
      }

      const domain = data.emailDomain;
      // 使用设置的位数，默认为12位
      const length = data.randomLength ? parseInt(data.randomLength) : 12;

      console.log('🔧 邮箱后缀:', domain);
      console.log('🔧 随机位数:', length);

      // 生成随机字符串
      for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }

      const randomEmail = result + '@' + domain;
      console.log('✅ 生成的随机邮箱:', randomEmail);

      // 如果是通过续杯按钮触发的，保存邮箱账户
      if (window.augmentRefillState && window.augmentRefillState.refillButtonClicked) {
        saveCreatedAccount(randomEmail);
      }

      resolve(randomEmail);
    });
  });
}

// 创建一个标志，用于跟踪按钮是否已添加
let buttonAdded = false;

// 使用防抖函数来限制checkUrl的调用频率
function debounce(func, wait) {
  let timeout;
  return function() {
    clearTimeout(timeout);
    timeout = setTimeout(func, wait);
  };
}

// 防抖处理的checkUrl函数
const debouncedCheckUrl = debounce(checkUrl, 300);

// 在页面变化时检查URL，但使用更精确的选择器和配置
const observer = new MutationObserver((mutations) => {
  // 只有当按钮尚未添加时才继续检查
  if (!buttonAdded) {
    debouncedCheckUrl();
  }
});

// 使用更精确的配置来观察DOM变化
observer.observe(document.body, {
  childList: true,
  subtree: true,
  attributes: false,
  characterData: false
});

// 等待验证码并自动填入
function waitForVerificationCodeAndFill(tempEmail) {
  console.log('🔄 Augment续杯: 开始等待验证码流程');
  console.log('📧 使用临时邮箱:', tempEmail);
  console.log('🌐 当前页面URL:', window.location.href);

  // 检查是否在验证码页面
  const checkVerificationPage = () => {
    const codeInput = document.querySelector('input[name="code"]');
    const hasCodeInput = codeInput !== null;
    console.log('🔍 检查验证码页面:', hasCodeInput ? '✅ 找到验证码输入框' : '❌ 未找到验证码输入框');
    if (hasCodeInput) {
      console.log('📝 验证码输入框元素:', codeInput);
    }
    return hasCodeInput;
  };

  // 如果不在验证码页面，等待页面跳转
  if (!checkVerificationPage()) {
    console.log('⏳ Augment续杯: 等待跳转到验证码页面...');
    let waitAttempts = 0;
    const waitForPage = setInterval(() => {
      waitAttempts++;
      console.log(`🔄 第${waitAttempts}次检查验证码页面...`);
      if (checkVerificationPage()) {
        clearInterval(waitForPage);
        console.log('✅ 成功跳转到验证码页面，开始获取验证码');
        startFetchingVerificationCode(tempEmail);
      }
    }, 1000);

    // 30秒后停止等待
    setTimeout(() => {
      clearInterval(waitForPage);
      console.log('⏰ Augment续杯: 等待验证码页面超时 (30秒)');
      alert('等待验证码页面超时，请手动输入验证码');
    }, 30000);
  } else {
    console.log('✅ 已在验证码页面，直接开始获取验证码');
    startFetchingVerificationCode(tempEmail);
  }
}

// 开始获取验证码
function startFetchingVerificationCode(tempEmail) {
  console.log('🚀 Augment续杯: 开始获取验证码流程');
  console.log('📧 目标临时邮箱:', tempEmail);

  let attempts = 0;
  const maxAttempts = 30; // 最多尝试30次，每次间隔2秒，总共1分钟

  const fetchCode = async () => {
    attempts++;
    console.log(`🔄 第${attempts}/${maxAttempts}次尝试获取验证码`);
    console.log('⏰ 当前时间:', new Date().toLocaleTimeString());

    try {
      const verificationCode = await getVerificationCodeFromTempMail(tempEmail);
      if (verificationCode) {
        console.log('🎉 成功获取到验证码:', verificationCode);
        fillVerificationCode(verificationCode);
        return;
      } else {
        console.log('📭 暂未获取到验证码，继续等待...');
      }
    } catch (error) {
      console.error('❌ 获取验证码时出错:', error);
      console.error('错误详情:', error.message);
      console.error('错误堆栈:', error.stack);
    }

    if (attempts < maxAttempts) {
      console.log(`⏳ 等待2秒后进行第${attempts + 1}次尝试...`);
      setTimeout(fetchCode, 2000); // 2秒后重试
    } else {
      console.log('⏰ 获取验证码超时，已尝试30次');
      alert('获取验证码超时，请手动输入验证码');
    }
  };

  // 延迟5秒开始第一次尝试，给邮件发送一些时间
  console.log('⏳ 延迟5秒后开始第一次尝试，给邮件发送留出时间...');
  setTimeout(fetchCode, 5000);
}

// 通过background script发起API请求
function sendMessageToBackground(message) {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, (response) => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
        return;
      }

      if (response.success) {
        resolve(response.data);
      } else {
        reject(new Error(response.error));
      }
    });
  });
}

// 从临时邮箱获取验证码
async function getVerificationCodeFromTempMail(tempEmail) {
  console.log('📡 开始调用临时邮箱API...');
  console.log('🔗 请求邮箱:', tempEmail);

  try {
    // 获取邮件列表
    const mailListUrl = `https://tempmail.plus/api/mails?email=${encodeURIComponent(tempEmail)}`;
    console.log('🌐 请求邮件列表URL:', mailListUrl);

    const mailListData = await sendMessageToBackground({
      action: 'fetchTempMail',
      url: mailListUrl
    });

    console.log('📋 邮件列表API响应数据:', JSON.stringify(mailListData, null, 2));

    if (!mailListData.result) {
      console.log('❌ 邮件列表API返回失败结果:', mailListData);
      return null;
    }

    if (!mailListData.mail_list || mailListData.mail_list.length === 0) {
      console.log('📭 暂无邮件');
      return null;
    }

    console.log(`📬 找到${mailListData.mail_list.length}封邮件，开始检查所有邮件...`);

    // 遍历邮件列表，获取所有邮件详情进行检查
    for (let i = 0; i < mailListData.mail_list.length; i++) {
      const mail = mailListData.mail_list[i];
      console.log(`📧 检查第${i + 1}封邮件:`, {
        id: mail.mail_id,
        from: mail.from_mail,
        subject: mail.subject,
        time: mail.time
      });

      try {
        // 获取邮件详情（不在列表阶段过滤，因为列表中的发件人可能不准确）
        const mailDetailUrl = `https://tempmail.plus/api/mails/${mail.mail_id}?email=${encodeURIComponent(tempEmail)}`;
        console.log('🌐 请求邮件详情URL:', mailDetailUrl);

        const mailDetailData = await sendMessageToBackground({
          action: 'fetchTempMail',
          url: mailDetailUrl
        });

        console.log('📄 邮件详情API响应数据:', JSON.stringify(mailDetailData, null, 2));

        if (mailDetailData.result) {
          console.log('📧 邮件详情 - 发件人:', mailDetailData.from_mail);
          console.log('📧 邮件详情 - 收件人:', mailDetailData.to);
          console.log('📧 邮件详情 - 主题:', mailDetailData.subject);

          // 检查是否来自Augment（使用详情中的准确发件人信息）
          if (mailDetailData.from_mail && mailDetailData.from_mail.includes('augmentcode.com')) {
            console.log('✅ 确认这是来自Augment的邮件');

            // 从邮件内容中提取验证码
            const verificationCode = extractVerificationCode(mailDetailData.html || mailDetailData.text);
            if (verificationCode) {
              console.log('🎯 成功提取验证码:', verificationCode);
              return verificationCode;
            } else {
              console.log('❌ 未能从邮件内容中提取验证码');
              console.log('📄 邮件HTML内容:', mailDetailData.html ? mailDetailData.html.substring(0, 500) + '...' : '无');
              console.log('📄 邮件文本内容:', mailDetailData.text ? mailDetailData.text.substring(0, 500) + '...' : '无');
            }
          } else {
            console.log('⏭️ 跳过非Augment邮件，实际发件人:', mailDetailData.from_mail);
          }
        } else {
          console.log('❌ 邮件详情API返回失败结果');
        }
      } catch (error) {
        console.error(`❌ 获取第${i + 1}封邮件详情时出错:`, error);
        // 继续处理下一封邮件
        continue;
      }
    }

    console.log('📭 未找到包含验证码的Augment邮件');
    return null;
  } catch (error) {
    console.error('❌ 获取临时邮件时出错:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    return null;
  }
}

// 从邮件内容中提取验证码
function extractVerificationCode(content) {
  console.log('🔍 开始提取验证码...');

  if (!content) {
    console.log('❌ 邮件内容为空');
    return null;
  }

  console.log('📄 邮件内容长度:', content.length);
  console.log('📄 邮件内容预览:', content.substring(0, 200) + '...');

  // 匹配验证码的正则表达式
  const patterns = [
    { name: 'HTML格式验证码', regex: /verification code is:\s*<b>(\d{6})<\/b>/i },
    { name: '文本格式验证码', regex: /verification code is:\s*(\d{6})/i },
    { name: '中文验证码', regex: /验证码[：:]\s*(\d{6})/i },
    { name: '通用code格式', regex: /code[：:]\s*(\d{6})/i },
    { name: '6位数字', regex: /(\d{6})/g }
  ];

  for (let i = 0; i < patterns.length; i++) {
    const pattern = patterns[i];
    console.log(`🔍 尝试模式${i + 1}: ${pattern.name}`);

    const match = content.match(pattern.regex);
    if (match) {
      console.log('✅ 找到匹配:', match);

      if (pattern.regex.global) {
        // 对于全局匹配，检查所有匹配项
        for (const matchItem of match) {
          if (/^\d{6}$/.test(matchItem)) {
            console.log('🎯 找到6位数字验证码:', matchItem);
            return matchItem;
          }
        }
      } else if (match[1]) {
        const code = match[1];
        console.log('🔍 提取的代码:', code);
        // 验证是否为6位数字
        if (/^\d{6}$/.test(code)) {
          console.log('✅ 验证码格式正确:', code);
          return code;
        } else {
          console.log('❌ 验证码格式不正确:', code);
        }
      }
    } else {
      console.log('❌ 未找到匹配');
    }
  }

  console.log('❌ 所有模式都未能提取到有效验证码');
  return null;
}

// 填入验证码并提交
function fillVerificationCode(code) {
  console.log('📝 开始填入验证码:', code);

  // 尝试多种选择器查找验证码输入框
  const codeInputSelectors = [
    'input[name="code"]',
    'input[id="code"]',
    'input[type="text"][autocomplete="off"]',
    '.input[name="code"]'
  ];

  let codeInput = null;
  for (const selector of codeInputSelectors) {
    codeInput = document.querySelector(selector);
    if (codeInput) {
      console.log('✅ 找到验证码输入框，选择器:', selector);
      break;
    }
  }

  if (codeInput) {
    console.log('✅ 验证码输入框元素:', codeInput);

    // 清空现有值
    codeInput.value = '';
    console.log('🧹 已清空输入框');

    // 设置新值
    codeInput.value = code;
    console.log('📝 验证码已设置到输入框');

    // 触发多种事件确保表单识别
    const events = ['input', 'change', 'keyup', 'blur'];
    events.forEach(eventType => {
      const event = new Event(eventType, { bubbles: true });
      codeInput.dispatchEvent(event);
      console.log(`🔄 已触发${eventType}事件`);
    });

    // 手动设置焦点
    codeInput.focus();
    console.log('🎯 已设置焦点');

    console.log('✅ 验证码已填入:', code);
    console.log('📝 当前输入框值:', codeInput.value);

    // 自动点击确认按钮
    setTimeout(() => {
      console.log('🔍 查找确认按钮...');

      const buttonSelectors = [
        'button[name="action"][value="default"]',
        'button[type="submit"]',
        'button[data-action-button-primary="true"]',
        '.c89213e2d.cec57cc42', // 从HTML中看到的样式类
        'button:contains("Continue")',
        'button:contains("确认")',
        'button:contains("Verify")',
        'button:contains("Submit")'
      ];

      let submitButton = null;
      for (const selector of buttonSelectors) {
        try {
          submitButton = document.querySelector(selector);
          if (submitButton) {
            console.log('✅ 找到确认按钮，选择器:', selector);
            console.log('🔘 按钮元素:', submitButton);
            break;
          }
        } catch (e) {
          // 忽略无效选择器错误
        }
      }

      // 如果没找到，尝试查找包含特定文本的按钮
      if (!submitButton) {
        const allButtons = document.querySelectorAll('button');
        console.log('🔍 查找所有按钮，总数:', allButtons.length);

        for (const button of allButtons) {
          const buttonText = button.textContent.trim().toLowerCase();
          console.log('🔘 检查按钮文本:', buttonText);

          if (buttonText.includes('continue') ||
              buttonText.includes('确认') ||
              buttonText.includes('verify') ||
              buttonText.includes('submit')) {
            submitButton = button;
            console.log('✅ 通过文本找到确认按钮:', buttonText);
            break;
          }
        }
      }

      if (submitButton) {
        console.log('🚀 准备点击确认按钮...');

        // 确保按钮可见和可点击
        submitButton.scrollIntoView();
        console.log('📍 按钮已滚动到视图中');

        // 尝试点击
        try {
          submitButton.click();
          console.log('✅ 已点击确认按钮');
        } catch (error) {
          console.error('❌ 点击按钮失败:', error);
          // 尝试触发表单提交
          const form = submitButton.closest('form');
          if (form) {
            console.log('🔄 尝试提交表单...');
            form.submit();
            console.log('✅ 已提交表单');
          }
        }
      } else {
        console.log('❌ 未找到确认按钮，请手动点击');
        console.log('🔍 页面所有按钮:');
        document.querySelectorAll('button').forEach((btn, index) => {
          console.log(`  按钮${index + 1}:`, {
            text: btn.textContent.trim(),
            name: btn.name,
            value: btn.value,
            type: btn.type,
            classes: btn.className,
            element: btn
          });
        });
      }
    }, 2000); // 增加延迟到2秒
  } else {
    console.error('❌ 未找到验证码输入框');
    console.log('🔍 页面所有输入框:');
    document.querySelectorAll('input').forEach((input, index) => {
      console.log(`  输入框${index + 1}:`, {
        name: input.name,
        id: input.id,
        type: input.type,
        classes: input.className,
        element: input
      });
    });
  }
}

// 检查是否应该自动获取验证码（基于邮箱域名）
function checkIfShouldAutoGetCode() {
  console.log('🔍 检查是否使用了配置的邮箱域名...');

  // 从页面中提取注册邮箱地址
  const emailElement = document.querySelector('.ulp-authenticator-selector-text');
  if (!emailElement) {
    console.error('❌ 未找到邮箱显示元素');
    return;
  }

  const registeredEmail = emailElement.textContent.trim();
  console.log('📧 页面显示的注册邮箱:', registeredEmail);

  // 获取配置的邮箱域名
  chrome.storage.sync.get(['emailDomain'], function(data) {
    if (!data.emailDomain) {
      console.log('❌ 未配置邮箱域名，跳过自动获取验证码');
      return;
    }

    const configuredDomain = data.emailDomain;
    console.log('🔧 配置的邮箱域名:', configuredDomain);

    // 检查注册邮箱是否使用了配置的域名
    if (registeredEmail.includes('@' + configuredDomain)) {
      console.log('✅ 检测到使用了配置的邮箱域名，开始自动获取验证码');
      window.augmentRefillState.autoVerificationStarted = true;
      startAutoVerificationProcess();
    } else {
      console.log('❌ 注册邮箱域名不匹配，不自动获取验证码');
      console.log('📧 注册邮箱:', registeredEmail);
      console.log('🔧 配置域名:', configuredDomain);
    }
  });
}

// 自动验证码获取流程（用于验证码页面）
function startAutoVerificationProcess() {
  console.log('🎯 开始自动验证码获取流程');

  // 从页面中提取注册邮箱地址
  const emailElement = document.querySelector('.ulp-authenticator-selector-text');
  if (!emailElement) {
    console.error('❌ 未找到邮箱显示元素');
    return;
  }

  const registeredEmail = emailElement.textContent.trim();
  console.log('📧 页面显示的注册邮箱:', registeredEmail);

  // 获取配置的临时邮箱地址
  getTempEmail().then(tempEmail => {
    console.log('📧 配置的临时邮箱:', tempEmail);
    console.log('🔄 开始获取验证码...');

    // 直接开始获取验证码流程
    startFetchingVerificationCode(tempEmail);
  }).catch(error => {
    console.error('❌ 获取临时邮箱配置失败:', error);
    alert('获取临时邮箱配置失败: ' + error.message);
  });
}

// 监听页面URL变化，重置状态
let lastUrl = window.location.href;
function checkUrlChange() {
  const currentUrl = window.location.href;
  if (currentUrl !== lastUrl) {
    console.log('🔄 页面URL发生变化');
    console.log('📍 从:', lastUrl);
    console.log('📍 到:', currentUrl);

    // 重置状态
    window.augmentRefillState = {
      refillButtonClicked: false,
      autoVerificationStarted: false,
      shouldAutoGetCode: false
    };
    console.log('🔄 已重置插件状态');

    lastUrl = currentUrl;
    // 延迟检查新页面
    setTimeout(checkUrl, 1000);
  }
}

// 定期检查URL变化
setInterval(checkUrlChange, 1000);

// 添加快速输入按钮
function addQuickInputButton() {
  console.log('🔘 开始添加快速输入按钮...');

  let attempts = 0;
  const maxAttempts = 20; // 最多尝试10秒

  // 等待邮箱输入框加载
  const checkExist = setInterval(() => {
    attempts++;
    console.log(`🔍 第${attempts}次查找邮箱输入框...`);

    const emailInput = document.querySelector('input[name="username"]');
    const existingQuickInputButton = document.querySelector('.quick-input-button-added');

    if (emailInput && !existingQuickInputButton) {
      clearInterval(checkExist);
      console.log('✅ 找到邮箱输入框，开始创建快速输入按钮');
      console.log('📧 邮箱输入框:', emailInput);

      // 创建快速输入按钮
      const quickInputButton = document.createElement('button');
      quickInputButton.type = 'button';
      quickInputButton.textContent = '📧';
      quickInputButton.className = 'quick-input-button-added';
      quickInputButton.title = '快速输入已创建的邮箱';

      // 设置按钮样式
      quickInputButton.style.cssText = `
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 30px;
        height: 30px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        cursor: pointer;
        font-size: 14px;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
      `;

      // 设置输入框容器为相对定位
      const inputContainer = emailInput.parentElement;
      if (inputContainer) {
        inputContainer.style.position = 'relative';
      }

      // 添加点击事件
      quickInputButton.addEventListener('click', showQuickInputMenu);

      // 将按钮插入到输入框容器中
      if (inputContainer) {
        inputContainer.appendChild(quickInputButton);
        console.log('📍 快速输入按钮已插入到DOM');
      } else {
        // 如果没有容器，插入到输入框后面
        emailInput.parentNode.insertBefore(quickInputButton, emailInput.nextSibling);
        console.log('📍 快速输入按钮已插入到输入框后面');
      }

      console.log('✅ 快速输入按钮添加完成');
    } else if (existingQuickInputButton) {
      clearInterval(checkExist);
      console.log('✅ 快速输入按钮已存在，跳过添加');
    } else if (attempts >= maxAttempts) {
      clearInterval(checkExist);
      console.log('⏰ 查找邮箱输入框超时，停止尝试');
    }
  }, 500);
}

// 显示快速输入菜单
function showQuickInputMenu() {
  console.log('📧 显示快速输入菜单');

  // 移除已存在的菜单
  const existingMenu = document.querySelector('.quick-input-menu');
  if (existingMenu) {
    existingMenu.remove();
  }

  // 获取已创建的账户
  chrome.storage.sync.get(['createdAccounts'], function(result) {
    const accounts = result.createdAccounts || [];

    if (accounts.length === 0) {
      console.log('📭 暂无已创建的邮箱账户');
      showTemporaryMessage('暂无已创建的邮箱账户');
      return;
    }

    console.log(`📋 找到${accounts.length}个已创建的账户`);

    // 创建菜单容器
    const menu = document.createElement('div');
    menu.className = 'quick-input-menu';
    menu.style.cssText = `
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      z-index: 1001;
      max-height: 300px;
      overflow-y: auto;
      min-width: 250px;
      margin-top: 5px;
    `;

    // 添加标题
    const title = document.createElement('div');
    title.textContent = '选择邮箱账户';
    title.style.cssText = `
      padding: 10px;
      background: #f5f5f5;
      border-bottom: 1px solid #ddd;
      font-weight: bold;
      font-size: 12px;
      color: #333;
    `;
    menu.appendChild(title);

    // 添加账户列表
    accounts.forEach((account, index) => {
      const item = document.createElement('div');
      item.style.cssText = `
        padding: 8px 10px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        font-size: 12px;
        transition: background-color 0.2s;
      `;

      item.innerHTML = `
        <div style="font-weight: 500; color: #333; margin-bottom: 2px;">${account.email}</div>
        <div style="font-size: 10px; color: #666;">${account.createdAt}</div>
      `;

      // 鼠标悬停效果
      item.addEventListener('mouseenter', () => {
        item.style.backgroundColor = '#f0f0f0';
      });

      item.addEventListener('mouseleave', () => {
        item.style.backgroundColor = 'white';
      });

      // 点击选择邮箱
      item.addEventListener('click', () => {
        selectEmail(account.email);
        menu.remove();
      });

      menu.appendChild(item);
    });

    // 获取快速输入按钮的位置
    const quickInputButton = document.querySelector('.quick-input-button-added');
    if (quickInputButton) {
      const buttonContainer = quickInputButton.parentElement;
      buttonContainer.appendChild(menu);

      // 点击其他地方关闭菜单
      setTimeout(() => {
        document.addEventListener('click', function closeMenu(e) {
          if (!menu.contains(e.target) && e.target !== quickInputButton) {
            menu.remove();
            document.removeEventListener('click', closeMenu);
          }
        });
      }, 100);
    }
  });
}

// 选择邮箱并填入
function selectEmail(email) {
  console.log('📧 选择邮箱:', email);

  const emailInput = document.querySelector('input[name="username"]');
  if (emailInput) {
    emailInput.value = email;

    // 触发各种事件确保表单识别
    const events = ['input', 'change', 'keyup', 'blur'];
    events.forEach(eventType => {
      const event = new Event(eventType, { bubbles: true });
      emailInput.dispatchEvent(event);
    });

    emailInput.focus();
    console.log('✅ 邮箱已填入:', email);

    showTemporaryMessage('邮箱已填入');
  } else {
    console.error('❌ 未找到邮箱输入框');
  }
}

// 显示临时消息
function showTemporaryMessage(message) {
  const messageDiv = document.createElement('div');
  messageDiv.textContent = message;
  messageDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4caf50;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  `;

  document.body.appendChild(messageDiv);

  setTimeout(() => {
    messageDiv.remove();
  }, 2000);
}

// 保存创建的邮箱账户
function saveCreatedAccount(email) {
  console.log('💾 保存创建的邮箱账户:', email);

  chrome.storage.sync.get(['createdAccounts'], function(result) {
    const accounts = result.createdAccounts || [];

    // 检查是否已存在相同的邮箱
    const existingIndex = accounts.findIndex(account => account.email === email);
    if (existingIndex !== -1) {
      console.log('📧 邮箱账户已存在，跳过保存');
      return;
    }

    // 创建新的账户记录
    const newAccount = {
      email: email,
      createdAt: new Date().toLocaleString('zh-CN'),
      timestamp: Date.now()
    };

    // 添加到账户列表（最新的在前面）
    accounts.unshift(newAccount);

    // 限制最多保存50个账户
    if (accounts.length > 50) {
      accounts.splice(50);
    }

    // 保存到存储
    chrome.storage.sync.set({ createdAccounts: accounts }, function() {
      console.log('✅ 邮箱账户已保存');
      console.log('📋 当前账户总数:', accounts.length);
    });
  });
}

// 初始检查
setTimeout(checkUrl, 500);
