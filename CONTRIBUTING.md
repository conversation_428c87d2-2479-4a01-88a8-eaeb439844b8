# 贡献指南

感谢您对Augment续杯插件项目的关注！我们欢迎各种形式的贡献。

## 🤝 如何贡献

### 报告问题

如果您发现了bug或有功能建议，请：

1. 检查 [Issues](../../issues) 中是否已有相关问题
2. 如果没有，请创建新的Issue
3. 使用清晰的标题和详细的描述
4. 提供复现步骤（如果是bug）
5. 包含您的环境信息（浏览器版本、插件版本等）

### 提交代码

1. **Fork项目**
   ```bash
   # 点击GitHub页面右上角的Fork按钮
   ```

2. **克隆您的Fork**
   ```bash
   git clone https://github.com/flintttan/augment-refill-extension.git
   cd augment-refill-extension
   ```

3. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   # 或者修复bug
   git checkout -b fix/your-bug-fix
   ```

4. **进行开发**
   ```bash
   # 安装依赖
   npm install
   
   # 开发和测试您的更改
   # 在Chrome中加载src目录进行测试
   ```

5. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   # 或者
   git commit -m "fix: fix your bug description"
   ```

6. **推送到您的Fork**
   ```bash
   git push origin feature/your-feature-name
   ```

7. **创建Pull Request**
   - 访问您的Fork页面
   - 点击"New Pull Request"
   - 填写详细的PR描述

## 📝 代码规范

### 提交信息格式

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

示例：
```
feat: add quick input feature for email selection
fix: resolve CORS issue with background script
docs: update README with installation instructions
```

### 代码风格

- 使用2个空格缩进
- 使用分号结尾
- 使用单引号字符串
- 函数和变量使用驼峰命名
- 常量使用大写下划线命名
- 添加适当的注释，特别是复杂逻辑

### 文件结构

```javascript
// 文件头部注释
// 功能描述和作者信息

// 全局变量和常量
const CONSTANT_VALUE = 'value';

// 主要功能函数
function mainFunction() {
  // 详细的功能实现
}

// 辅助函数
function helperFunction() {
  // 辅助功能实现
}

// 事件监听和初始化
document.addEventListener('DOMContentLoaded', () => {
  // 初始化代码
});
```

## 🧪 测试

在提交PR之前，请确保：

1. **功能测试**
   - 在Chrome浏览器中加载插件
   - 测试所有相关功能
   - 验证新功能不会破坏现有功能

2. **兼容性测试**
   - 测试不同版本的Chrome浏览器
   - 确保在不同操作系统上正常工作

3. **错误处理**
   - 测试异常情况的处理
   - 确保有适当的错误提示

## 📚 文档

如果您的更改涉及：

- 新功能：请更新相关文档
- API变更：请更新技术文档
- 配置变更：请更新使用说明
- 重大变更：请更新CHANGELOG.md

## 🔍 代码审查

所有的PR都会经过代码审查：

- 确保代码质量和一致性
- 验证功能的正确性
- 检查是否有潜在的安全问题
- 确保文档的完整性

## 📋 PR检查清单

提交PR前请确认：

- [ ] 代码遵循项目的编码规范
- [ ] 添加了适当的注释和文档
- [ ] 测试了所有相关功能
- [ ] 更新了相关文档
- [ ] 提交信息格式正确
- [ ] 没有引入新的警告或错误

## 🎯 开发重点

当前项目的重点发展方向：

1. **功能完善**
   - 提升用户体验
   - 增加更多自动化功能
   - 优化性能和稳定性

2. **兼容性**
   - 支持更多浏览器
   - 适配不同版本的目标网站
   - 提升跨平台兼容性

3. **安全性**
   - 加强隐私保护
   - 优化数据安全
   - 防止恶意使用

## 💬 交流

如果您有任何问题或建议：

- 创建 [Issue](../../issues/new) 进行讨论
- 在PR中留言交流
- 查看现有的 [Discussions](../../discussions)

## 📄 许可证

通过贡献代码，您同意您的贡献将在 [MIT License](LICENSE) 下发布。

---

再次感谢您的贡献！🎉
