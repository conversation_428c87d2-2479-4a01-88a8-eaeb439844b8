# Augment续杯 浏览器插件

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Chrome Extension](https://img.shields.io/badge/Chrome-Extension-blue.svg)](https://developer.chrome.com/docs/extensions/)
[![JavaScript](https://img.shields.io/badge/JavaScript-ES6+-yellow.svg)](https://developer.mozilla.org/en-US/docs/Web/JavaScript)

这是一个Chrome浏览器扩展，用于在Augment登录页面添加"续杯"功能，支持自动获取验证码和邮箱账户管理。

## 项目来源

本项目基于 [lisniuse/free-augment-code](https://github.com/lisniuse/free-augment-code) 进行开发和功能增强。

> **重要提示：** 使用前请在扩展设置中配置邮箱后缀和临时邮箱前缀。未正确配置将无法使用续杯功能！

## ✨ 主要功能

### 🚀 核心功能
- **一键续杯**: 在Augment登录页面添加"续杯"按钮，一键完成注册流程
- **自动验证码**: 自动获取临时邮箱验证码并填入，无需手动操作
- **智能检测**: 自动检测邮箱域名，匹配时自动触发验证码获取
- **完全自动化**: 从邮箱生成到验证码填入，全程自动化处理

### 📧 邮箱管理
- **账户保存**: 自动保存通过续杯创建的所有邮箱账户
- **时间记录**: 显示每个账户的详细创建时间
- **快速操作**: 支持复制、删除单个账户或清空所有账户
- **智能排序**: 按创建时间倒序排列，最新账户在最前面

### ⚡ 快速输入
- **快速选择**: 登录页面邮箱输入框旁的📧按钮，快速选择已创建账户
- **一键填入**: 点击任意邮箱即可快速填入到输入框
- **自动触发**: 填入后自动触发后续的验证码获取流程

### 🔧 自定义配置
- **邮箱后缀**: 自定义用于生成注册邮箱的域名
- **临时邮箱前缀**: 自定义用于接收验证码的邮箱前缀
- **随机位数**: 自定义随机字符串长度（默认12位）
- **隐私保护**: 分离式配置设计，保护个人信息

## 📦 安装方法

### 方法一：下载发布版本（推荐）

1. 前往 [Releases](../../releases) 页面下载最新版本
2. 解压ZIP文件到任意文件夹
3. 打开Chrome浏览器，访问 `chrome://extensions/`
4. 开启右上角的"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择解压后的文件夹

### 方法二：从源码构建

```bash
# 克隆仓库
git clone https://github.com/flintttan/augment-refill-extension.git
cd augment-refill-extension

# 安装依赖
npm install

# 构建扩展
npm run build

# 构建完成后，在Chrome中加载 dist 文件夹
```

### 方法三：开发模式

```bash
# 克隆仓库
git clone https://github.com/flintttan/augment-refill-extension.git
cd augment-refill-extension

# 直接在Chrome中加载 src 文件夹（开发模式）
```

## 🚀 快速开始

### 1. 配置插件

安装扩展后，点击扩展图标进行配置：

- **邮箱后缀**（如 `example.com`）：用于生成注册邮箱
- **临时邮箱前缀**（如 `myname123`）：用于接收验证码，系统会自动添加 `@mailto.plus` 后缀
- **随机字符串位数**（默认12位）：控制生成邮箱的随机部分长度

### 2. 使用方式

#### 方式一：一键续杯（推荐）
1. 访问 [Augment登录页面](https://login.augmentcode.com/)
2. 点击页面上的"续杯"按钮
3. 插件自动完成注册和验证流程

#### 方式二：快速输入
1. 在登录页面点击邮箱输入框旁的📧按钮
2. 选择之前创建的邮箱账户
3. 自动触发验证码获取流程

#### 方式三：智能检测
1. 手动输入使用配置域名的邮箱
2. 插件自动检测域名匹配
3. 自动获取验证码完成登录

### 3. 账户管理

- **自动保存**：续杯创建的账户自动保存
- **快速复制**：一键复制邮箱地址
- **时间记录**：查看账户创建时间
- **批量管理**：支持删除单个或清空所有账户

> **提示：** 首次使用前请确保正确配置邮箱后缀和临时邮箱前缀。

## 🔧 技术原理

### 工作流程

1. **邮箱生成**: 根据配置生成随机邮箱地址
2. **自动填充**: 将邮箱填入登录表单
3. **验证码获取**: 通过临时邮箱API获取验证码
4. **自动验证**: 提取验证码并自动填入完成验证

### 核心技术

- **DOM操作**: 自动化表单填写和按钮点击
- **API集成**: 集成临时邮箱服务API
- **正则匹配**: 智能提取邮件中的验证码
- **状态管理**: 防止意外触发和重复执行
- **CORS处理**: 通过Background Script解决跨域问题

### 隐私保护

- **分离式配置**: 用户只需输入邮箱前缀，系统自动添加域名后缀
- **本地存储**: 所有数据存储在用户本地，不上传到服务器
- **加密同步**: 通过Chrome同步服务安全传输数据

## 📁 项目结构

```
augment-refill-extension/
├── dist/                    # 构建输出目录
├── scripts/                 # 构建脚本
│   └── build.js            # 构建脚本
├── src/                     # 源代码
│   ├── background.js        # 后台脚本（处理API请求）
│   ├── content.js           # 内容脚本（页面交互）
│   ├── popup.html           # 设置页面HTML
│   ├── popup.js             # 设置页面脚本
│   ├── manifest.json        # 扩展配置文件
│   └── icon.ico             # 扩展图标
├── docs/                    # 文档目录
│   ├── USAGE.md             # 使用说明
│   ├── DEBUG_GUIDE.md       # 调试指南
│   ├── ACCOUNT_MANAGEMENT.md # 账户管理说明
│   ├── QUICK_INPUT_FEATURE.md # 快速输入功能
│   ├── FEATURE_EXPLANATION.md # 功能详解
│   ├── PRIVACY_PROTECTION.md  # 隐私保护
│   └── CORS_SOLUTION.md      # CORS解决方案
├── package.json             # 项目配置
└── README.md                # 项目说明
```

## 🛠️ 开发指南

### 环境要求

- Node.js (推荐 v14 或更高版本)
- npm (推荐 v6 或更高版本)
- Chrome浏览器

### 开发流程

```bash
# 克隆项目
git clone https://github.com/flintttan/augment-refill-extension.git
cd augment-refill-extension

# 安装依赖
npm install

# 开发模式（直接加载src目录）
# 在Chrome扩展管理页面加载 src 文件夹

# 构建生产版本
npm run build
```

### 调试方法

1. **Content Script调试**: 在目标页面按F12查看Console
2. **Background Script调试**: 在扩展管理页面点击"检查视图 service worker"
3. **Popup调试**: 右键点击扩展图标选择"检查弹出内容"

详细调试指南请参考 [DEBUG_GUIDE.md](docs/DEBUG_GUIDE.md)

## 📖 文档

- [使用说明](docs/USAGE.md) - 详细的使用指南
- [功能详解](docs/FEATURE_EXPLANATION.md) - 功能原理和使用场景
- [账户管理](docs/ACCOUNT_MANAGEMENT.md) - 邮箱账户管理功能
- [快速输入](docs/QUICK_INPUT_FEATURE.md) - 快速输入功能说明
- [隐私保护](docs/PRIVACY_PROTECTION.md) - 隐私保护设计
- [调试指南](docs/DEBUG_GUIDE.md) - 问题排查和调试方法
- [CORS解决方案](docs/CORS_SOLUTION.md) - 跨域问题解决方案

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 提交Issue

- 使用清晰的标题描述问题
- 提供详细的复现步骤
- 包含浏览器版本和插件版本信息
- 附上相关的控制台日志

### 提交PR

1. Fork本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源。

## 🙏 致谢

- 感谢 [lisniuse/free-augment-code](https://github.com/lisniuse/free-augment-code) 提供的原始项目基础
- 感谢 [tempmail.plus](https://tempmail.plus/) 提供的临时邮箱API服务
- 感谢所有贡献者和用户的支持

## ⚠️ 免责声明

本插件仅供学习和研究使用，请遵守相关网站的服务条款和法律法规。使用本插件产生的任何后果由用户自行承担。

## 📞 支持

如果您在使用过程中遇到问题：

1. 查看 [使用说明](docs/USAGE.md) 和 [调试指南](docs/DEBUG_GUIDE.md)
2. 搜索已有的 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new) 描述问题

---

**⭐ 如果这个项目对您有帮助，请给个Star支持一下！**
