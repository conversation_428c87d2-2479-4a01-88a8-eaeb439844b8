// 弹出窗口脚本
document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const emailDomainInput = document.getElementById('emailDomain');
  const tempEmailPrefixInput = document.getElementById('tempEmailPrefix');
  const randomLengthInput = document.getElementById('randomLength');
  const saveButton = document.getElementById('saveButton');
  const statusMessage = document.getElementById('statusMessage');
  const link = document.querySelector('a');
  const increaseBtn = document.getElementById('increaseBtn');
  const decreaseBtn = document.getElementById('decreaseBtn');
  const currentValue = document.getElementById('currentValue');
  const emailAccountsList = document.getElementById('emailAccountsList');
  const clearAllButton = document.getElementById('clearAllButton');

  // 更新当前值显示
  function updateCurrentValue() {
    const value = randomLengthInput.value.trim();
    if (value) {
      currentValue.textContent = value + '位';
    } else {
      currentValue.textContent = '12位(默认)';
    }
  }

  // 从存储中加载保存的设置
  chrome.storage.sync.get(['emailDomain', 'tempEmailPrefix', 'randomLength', 'createdAccounts'], function(result) {
    if (result.emailDomain) {
      emailDomainInput.value = result.emailDomain;
    }

    if (result.tempEmailPrefix) {
      tempEmailPrefixInput.value = result.tempEmailPrefix;
    }

    // 设置随机字符串位数
    if (result.randomLength) {
      randomLengthInput.value = result.randomLength;
    }

    // 初始化显示当前值
    updateCurrentValue();

    // 加载并显示已创建的账户
    loadCreatedAccounts(result.createdAccounts || []);
  });

  // 增加按钮点击事件
  increaseBtn.addEventListener('click', function() {
    const currentVal = parseInt(randomLengthInput.value) || 0;
    if (currentVal < 32) {
      randomLengthInput.value = currentVal + 1;
      updateCurrentValue();
    }
  });

  // 减少按钮点击事件
  decreaseBtn.addEventListener('click', function() {
    const currentVal = parseInt(randomLengthInput.value) || 2;
    if (currentVal > 1) {
      randomLengthInput.value = currentVal - 1;
      updateCurrentValue();
    }
  });

  // 输入框值变化事件
  randomLengthInput.addEventListener('input', updateCurrentValue);

  // 保存按钮点击事件
  saveButton.addEventListener('click', function() {
    let domain = emailDomainInput.value.trim();
    let tempEmailPrefix = tempEmailPrefixInput.value.trim();
    let length = randomLengthInput.value.trim();

    // 如果用户输入了@，自动去除（仅对邮箱后缀）
    if (domain.startsWith('@')) {
      domain = domain.substring(1);
    }

    // 验证邮箱后缀输入是否为空
    if (!domain) {
      statusMessage.textContent = '请输入有效的邮箱后缀';
      statusMessage.style.color = '#f44336';
      return;
    }

    // 验证临时邮箱前缀输入是否为空
    if (!tempEmailPrefix) {
      statusMessage.textContent = '请输入有效的临时邮箱前缀';
      statusMessage.style.color = '#f44336';
      return;
    }

    // 验证邮箱后缀格式
    if (domain.includes('@') || !domain.includes('.')) {
      statusMessage.textContent = '请输入正确的域名格式，如 example.com';
      statusMessage.style.color = '#f44336';
      return;
    }

    // 验证临时邮箱前缀格式（只能包含字母、数字、下划线、连字符）
    const prefixRegex = /^[a-zA-Z0-9_-]+$/;
    if (!prefixRegex.test(tempEmailPrefix)) {
      statusMessage.textContent = '临时邮箱前缀只能包含字母、数字、下划线和连字符';
      statusMessage.style.color = '#f44336';
      return;
    }

    // 验证随机字符串位数
    if (length && (isNaN(length) || parseInt(length) < 1 || parseInt(length) > 32)) {
      statusMessage.textContent = '请输入1-32之间的有效位数';
      statusMessage.style.color = '#f44336';
      return;
    }

    // 准备要保存的数据
    const dataToSave = {
      emailDomain: domain,
      tempEmailPrefix: tempEmailPrefix
    };

    // 如果用户设置了位数，则保存
    if (length) {
      dataToSave.randomLength = length;
    }

    // 保存到Chrome存储
    chrome.storage.sync.set(dataToSave, function() {
      statusMessage.textContent = '设置已保存';
      statusMessage.style.color = '#4caf50';

      // 更新显示
      updateCurrentValue();

      // 3秒后清除状态消息
      setTimeout(function() {
        statusMessage.textContent = '';
      }, 3000);
    });
  });

  // 加载并显示已创建的账户
  function loadCreatedAccounts(accounts) {
    console.log('加载已创建账户:', accounts);

    if (!accounts || accounts.length === 0) {
      emailAccountsList.innerHTML = '<div class="no-accounts">暂无已创建的邮箱账户</div>';
      return;
    }

    emailAccountsList.innerHTML = '';
    accounts.forEach((account, index) => {
      const accountItem = createAccountItem(account, index);
      emailAccountsList.appendChild(accountItem);
    });
  }

  // 创建账户项元素
  function createAccountItem(account, index) {
    const item = document.createElement('div');
    item.className = 'email-account-item';

    // 创建头部区域（邮箱地址和操作按钮）
    const header = document.createElement('div');
    header.className = 'email-account-header';

    const emailText = document.createElement('div');
    emailText.className = 'email-account-text';
    emailText.textContent = account.email;

    const actions = document.createElement('div');
    actions.className = 'email-account-actions';

    // 复制按钮
    const copyBtn = document.createElement('button');
    copyBtn.className = 'email-account-btn copy-btn';
    copyBtn.textContent = '复制';
    copyBtn.addEventListener('click', () => copyToClipboard(account.email));

    // 删除按钮
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'email-account-btn delete-btn';
    deleteBtn.textContent = '删除';
    deleteBtn.addEventListener('click', () => deleteAccount(index));

    actions.appendChild(copyBtn);
    actions.appendChild(deleteBtn);

    header.appendChild(emailText);
    header.appendChild(actions);

    // 创建时间显示
    const timeText = document.createElement('div');
    timeText.className = 'email-account-time';
    timeText.textContent = `创建时间: ${account.createdAt}`;

    item.appendChild(header);
    item.appendChild(timeText);

    return item;
  }

  // 复制到剪贴板
  function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
      showStatus('邮箱地址已复制到剪贴板', '#4caf50');
    }).catch(err => {
      console.error('复制失败:', err);
      showStatus('复制失败，请手动复制', '#f44336');
    });
  }

  // 删除账户
  function deleteAccount(index) {
    chrome.storage.sync.get(['createdAccounts'], function(result) {
      const accounts = result.createdAccounts || [];
      if (index >= 0 && index < accounts.length) {
        accounts.splice(index, 1);
        chrome.storage.sync.set({ createdAccounts: accounts }, function() {
          loadCreatedAccounts(accounts);
          showStatus('账户已删除', '#4caf50');
        });
      }
    });
  }

  // 清空所有账户
  clearAllButton.addEventListener('click', function() {
    if (confirm('确定要清空所有已创建的邮箱账户吗？此操作不可撤销。')) {
      chrome.storage.sync.set({ createdAccounts: [] }, function() {
        loadCreatedAccounts([]);
        showStatus('所有账户已清空', '#4caf50');
      });
    }
  });

  // 显示状态消息
  function showStatus(message, color) {
    statusMessage.textContent = message;
    statusMessage.style.color = color;
    setTimeout(function() {
      statusMessage.textContent = '';
    }, 3000);
  }

  // 添加点击事件，在新标签页中打开链接
  if (link) {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      chrome.tabs.create({ url: this.href });
    });
  }
});
